package com.ybda.service;

import com.ybda.protocol.t808.T0200;
import java.util.Map;

/**
 * 车辆状态分析服务接口
 * 提供车辆状态位和报警位的分析功能
 */
public interface VehicleStatusService {

    /**
     * 分析车辆状态信息
     * @param t0200 位置信息
     * @return 完整的状态分析结果
     */
    Map<String, Object> analyzeVehicleStatus(T0200 t0200);

    /**
     * 分析状态位信息
     * @param statusBit 状态位
     * @return 状态位分析结果
     */
    Map<String, Object> analyzeStatusBit(int statusBit);

    /**
     * 分析报警位信息
     * @param alarmBit 报警位
     * @return 报警位分析结果
     */
    Map<String, Object> analyzeAlarmBit(int alarmBit);

    /**
     * 获取车辆综合状态描述
     * @param statusBit 状态位
     * @param alarmBit 报警位
     * @return 综合状态描述
     */
    String getVehicleStatusSummary(int statusBit, int alarmBit);

    /**
     * 检查车辆是否存在异常
     * @param statusBit 状态位
     * @param alarmBit 报警位
     * @return 是否存在异常
     */
    boolean hasAbnormalStatus(int statusBit, int alarmBit);

    /**
     * 获取车辆状态等级
     * @param statusBit 状态位
     * @param alarmBit 报警位
     * @return 状态等级（正常、警告、异常、紧急）
     */
    String getVehicleStatusLevel(int statusBit, int alarmBit);

    /**
     * 判断车辆是否在运行状态
     * @param statusBit 状态位
     * @param speedKph 速度
     * @return 是否在运行
     */
    boolean isVehicleRunning(int statusBit, Double speedKph);

    /**
     * 获取车辆运行状态描述
     * @param statusBit 状态位
     * @param speedKph 速度
     * @return 运行状态描述
     */
    String getRunningStatusDescription(int statusBit, Double speedKph);
}
