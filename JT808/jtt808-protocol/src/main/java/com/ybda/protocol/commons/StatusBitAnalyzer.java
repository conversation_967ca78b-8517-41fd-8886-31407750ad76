package com.ybda.protocol.commons;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JT808协议状态位解析器
 * 用于解析32位状态标志位的各个状态信息
 * 
 * 状态位定义（按位从低到高）：
 * 位0：0-ACC关，1-ACC开
 * 位1：0-定位，1-未定位  
 * 位2：0-北纬，1-南纬
 * 位3：0-东经，1-西经
 * 位4：0-运营状态，1-停运状态
 * 位5：0-经纬度未经保密插件加密，1-经纬度已经保密插件加密
 * 位6-7：保留
 * 位8-9：00-空车，01-半载，10-保留，11-满载
 * 位10：0-车辆油路正常，1-车辆油路断开
 * 位11：0-车辆电路正常，1-车辆电路断开  
 * 位12：0-车门解锁，1-车门加锁
 * 位13：0-门1关，1-门1开
 * 位14：0-门2关，1-门2开
 * 位15：0-门3关，1-门3开
 * 位16：0-门4关，1-门4开
 * 位17：0-门5关，1-门5开
 * 位18：0-GPS定位，1-北斗定位
 * 位19：0-GPS定位，1-GLONASS定位
 * 位20：0-GPS定位，1-Galileo定位
 * 位21-31：保留
 */
public class StatusBitAnalyzer {

    /**
     * 解析状态位，返回详细的状态信息
     * @param statusBit 32位状态标志位
     * @return 状态信息映射
     */
    public static Map<String, Object> analyzeStatusBit(int statusBit) {
        Map<String, Object> statusInfo = new HashMap<>();
        
        // 基础状态
        statusInfo.put("accStatus", Bit.isTrue(statusBit, 0) ? "开启" : "关闭");
        statusInfo.put("isPositioned", !Bit.isTrue(statusBit, 1)); // 位1为0表示定位
        statusInfo.put("positionStatus", Bit.isTrue(statusBit, 1) ? "未定位" : "已定位");
        statusInfo.put("latitude", Bit.isTrue(statusBit, 2) ? "南纬" : "北纬");
        statusInfo.put("longitude", Bit.isTrue(statusBit, 3) ? "西经" : "东经");
        statusInfo.put("operationStatus", Bit.isTrue(statusBit, 4) ? "停运" : "运营");
        statusInfo.put("isEncrypted", Bit.isTrue(statusBit, 5));
        statusInfo.put("encryptionStatus", Bit.isTrue(statusBit, 5) ? "已加密" : "未加密");
        
        // 载重状态（位8-9）
        int loadStatus = (statusBit >> 8) & 0x03;
        String loadDesc;
        switch (loadStatus) {
            case 0: loadDesc = "空车"; break;
            case 1: loadDesc = "半载"; break;
            case 2: loadDesc = "保留"; break;
            case 3: loadDesc = "满载"; break;
            default: loadDesc = "未知"; break;
        }
        statusInfo.put("loadStatus", loadDesc);
        statusInfo.put("loadStatusCode", loadStatus);
        
        // 车辆系统状态
        statusInfo.put("oilCircuit", Bit.isTrue(statusBit, 10) ? "断开" : "正常");
        statusInfo.put("electricCircuit", Bit.isTrue(statusBit, 11) ? "断开" : "正常");
        statusInfo.put("doorLock", Bit.isTrue(statusBit, 12) ? "加锁" : "解锁");
        
        // 车门状态
        statusInfo.put("door1", Bit.isTrue(statusBit, 13) ? "开启" : "关闭");
        statusInfo.put("door2", Bit.isTrue(statusBit, 14) ? "开启" : "关闭");
        statusInfo.put("door3", Bit.isTrue(statusBit, 15) ? "开启" : "关闭");
        statusInfo.put("door4", Bit.isTrue(statusBit, 16) ? "开启" : "关闭");
        statusInfo.put("door5", Bit.isTrue(statusBit, 17) ? "开启" : "关闭");
        
        // 定位系统类型
        List<String> positioningSystems = new ArrayList<>();
        if (Bit.isTrue(statusBit, 18)) {
            positioningSystems.add("北斗");
        } else {
            positioningSystems.add("GPS");
        }
        if (Bit.isTrue(statusBit, 19)) {
            positioningSystems.add("GLONASS");
        }
        if (Bit.isTrue(statusBit, 20)) {
            positioningSystems.add("Galileo");
        }
        statusInfo.put("positioningSystems", positioningSystems);
        statusInfo.put("positioningSystemsDesc", String.join("+", positioningSystems));
        
        // 原始状态位值
        statusInfo.put("rawStatusBit", statusBit);
        statusInfo.put("statusBitHex", String.format("0x%08X", statusBit));
        
        return statusInfo;
    }
    
    /**
     * 获取车辆综合状态描述
     * @param statusBit 状态位
     * @return 综合状态描述
     */
    public static String getVehicleStatusSummary(int statusBit) {
        StringBuilder summary = new StringBuilder();
        
        // ACC状态
        if (Bit.isTrue(statusBit, 0)) {
            summary.append("ACC开启");
        } else {
            summary.append("ACC关闭");
        }
        
        // 定位状态
        if (!Bit.isTrue(statusBit, 1)) {
            summary.append("·已定位");
        } else {
            summary.append("·未定位");
        }
        
        // 运营状态
        if (Bit.isTrue(statusBit, 4)) {
            summary.append("·停运");
        } else {
            summary.append("·运营中");
        }
        
        // 载重状态
        int loadStatus = (statusBit >> 8) & 0x03;
        switch (loadStatus) {
            case 0: summary.append("·空车"); break;
            case 1: summary.append("·半载"); break;
            case 3: summary.append("·满载"); break;
        }
        
        // 异常状态检查
        List<String> abnormalStatus = new ArrayList<>();
        if (Bit.isTrue(statusBit, 10)) {
            abnormalStatus.add("油路断开");
        }
        if (Bit.isTrue(statusBit, 11)) {
            abnormalStatus.add("电路断开");
        }
        
        if (!abnormalStatus.isEmpty()) {
            summary.append("·异常:").append(String.join(",", abnormalStatus));
        }
        
        return summary.toString();
    }
    
    /**
     * 检查是否存在异常状态
     * @param statusBit 状态位
     * @return 是否存在异常
     */
    public static boolean hasAbnormalStatus(int statusBit) {
        // 未定位
        if (Bit.isTrue(statusBit, 1)) {
            return true;
        }
        // 油路断开
        if (Bit.isTrue(statusBit, 10)) {
            return true;
        }
        // 电路断开
        if (Bit.isTrue(statusBit, 11)) {
            return true;
        }
        return false;
    }
    
    /**
     * 获取异常状态列表
     * @param statusBit 状态位
     * @return 异常状态列表
     */
    public static List<String> getAbnormalStatusList(int statusBit) {
        List<String> abnormalList = new ArrayList<>();
        
        if (Bit.isTrue(statusBit, 1)) {
            abnormalList.add("GPS未定位");
        }
        if (Bit.isTrue(statusBit, 10)) {
            abnormalList.add("车辆油路断开");
        }
        if (Bit.isTrue(statusBit, 11)) {
            abnormalList.add("车辆电路断开");
        }
        
        return abnormalList;
    }
    
    /**
     * 判断车辆是否在运行状态
     * @param statusBit 状态位
     * @return 是否在运行
     */
    public static boolean isVehicleRunning(int statusBit) {
        // ACC开启且未停运且定位正常
        return Bit.isTrue(statusBit, 0) && !Bit.isTrue(statusBit, 4) && !Bit.isTrue(statusBit, 1);
    }
    
    /**
     * 获取车门开启状态列表
     * @param statusBit 状态位
     * @return 开启的车门列表
     */
    public static List<String> getOpenDoors(int statusBit) {
        List<String> openDoors = new ArrayList<>();
        
        if (Bit.isTrue(statusBit, 13)) openDoors.add("门1");
        if (Bit.isTrue(statusBit, 14)) openDoors.add("门2");
        if (Bit.isTrue(statusBit, 15)) openDoors.add("门3");
        if (Bit.isTrue(statusBit, 16)) openDoors.add("门4");
        if (Bit.isTrue(statusBit, 17)) openDoors.add("门5");
        
        return openDoors;
    }
}
