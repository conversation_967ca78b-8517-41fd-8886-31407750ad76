package com.ybda.protocol.commons;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JT808协议报警位解析器
 * 用于解析32位报警标志位的各个报警信息
 * 
 * 报警位定义（按位从低到高）：
 * 位0：紧急报警，触动报警开关后触发
 * 位1：超速报警
 * 位2：疲劳驾驶
 * 位3：危险预警
 * 位4：GNSS模块发生故障
 * 位5：GNSS天线未接或被剪断
 * 位6：GNSS天线短路
 * 位7：终端主电源欠压
 * 位8：终端主电源掉电
 * 位9：终端LCD或显示器故障
 * 位10：TTS模块故障
 * 位11：摄像头故障
 * 位12：道路运输证IC卡模块故障
 * 位13：超速预警
 * 位14：疲劳驾驶预警
 * 位15：违规行驶
 * 位16：胎压异常
 * 位17：右转盲区异常
 * 位18：当天累计驾驶超时
 * 位19：超时停车
 * 位20：进出区域
 * 位21：进出路线
 * 位22：路段行驶时间不足或过长
 * 位23：路线偏离报警
 * 位24：车辆VSS故障
 * 位25：车辆油量异常
 * 位26：车辆被盗（通过车辆防盗器）
 * 位27：车辆非法点火
 * 位28：车辆非法位移
 * 位29：碰撞预警
 * 位30：侧翻预警
 * 位31：非法开门报警
 */
public class AlarmBitAnalyzer {

    /**
     * 报警类型枚举
     */
    public enum AlarmType {
        EMERGENCY(0, "紧急报警", "高"),
        OVERSPEED(1, "超速报警", "高"),
        FATIGUE_DRIVING(2, "疲劳驾驶", "高"),
        DANGER_WARNING(3, "危险预警", "高"),
        GNSS_FAULT(4, "GNSS模块故障", "中"),
        GNSS_ANTENNA_DISCONNECT(5, "GNSS天线断开", "中"),
        GNSS_ANTENNA_SHORT(6, "GNSS天线短路", "中"),
        POWER_UNDERVOLTAGE(7, "主电源欠压", "中"),
        POWER_OFF(8, "主电源掉电", "高"),
        LCD_FAULT(9, "LCD显示器故障", "低"),
        TTS_FAULT(10, "TTS模块故障", "低"),
        CAMERA_FAULT(11, "摄像头故障", "中"),
        IC_CARD_FAULT(12, "IC卡模块故障", "低"),
        OVERSPEED_WARNING(13, "超速预警", "中"),
        FATIGUE_WARNING(14, "疲劳驾驶预警", "中"),
        ILLEGAL_DRIVING(15, "违规行驶", "中"),
        TIRE_PRESSURE_ABNORMAL(16, "胎压异常", "中"),
        RIGHT_BLIND_SPOT(17, "右转盲区异常", "中"),
        DAILY_DRIVING_OVERTIME(18, "当天累计驾驶超时", "中"),
        OVERTIME_PARKING(19, "超时停车", "低"),
        AREA_IN_OUT(20, "进出区域", "低"),
        ROUTE_IN_OUT(21, "进出路线", "低"),
        ROUTE_TIME_ABNORMAL(22, "路段行驶时间异常", "中"),
        ROUTE_DEVIATION(23, "路线偏离", "中"),
        VSS_FAULT(24, "车辆VSS故障", "中"),
        FUEL_ABNORMAL(25, "车辆油量异常", "中"),
        VEHICLE_STOLEN(26, "车辆被盗", "高"),
        ILLEGAL_IGNITION(27, "车辆非法点火", "高"),
        ILLEGAL_DISPLACEMENT(28, "车辆非法位移", "高"),
        COLLISION_WARNING(29, "碰撞预警", "高"),
        ROLLOVER_WARNING(30, "侧翻预警", "高"),
        ILLEGAL_DOOR_OPEN(31, "非法开门", "中");

        private final int bit;
        private final String description;
        private final String level;

        AlarmType(int bit, String description, String level) {
            this.bit = bit;
            this.description = description;
            this.level = level;
        }

        public int getBit() { return bit; }
        public String getDescription() { return description; }
        public String getLevel() { return level; }
    }

    /**
     * 解析报警位，返回详细的报警信息
     * @param alarmBit 32位报警标志位
     * @return 报警信息映射
     */
    public static Map<String, Object> analyzeAlarmBit(int alarmBit) {
        Map<String, Object> alarmInfo = new HashMap<>();
        List<Map<String, Object>> activeAlarms = new ArrayList<>();
        List<String> alarmDescriptions = new ArrayList<>();
        
        // 检查每个报警位
        for (AlarmType alarmType : AlarmType.values()) {
            if (Bit.isTrue(alarmBit, alarmType.getBit())) {
                Map<String, Object> alarm = new HashMap<>();
                alarm.put("bit", alarmType.getBit());
                alarm.put("type", alarmType.name());
                alarm.put("description", alarmType.getDescription());
                alarm.put("level", alarmType.getLevel());
                activeAlarms.add(alarm);
                alarmDescriptions.add(alarmType.getDescription());
            }
        }
        
        alarmInfo.put("hasAlarm", !activeAlarms.isEmpty());
        alarmInfo.put("alarmCount", activeAlarms.size());
        alarmInfo.put("activeAlarms", activeAlarms);
        alarmInfo.put("alarmDescriptions", alarmDescriptions);
        alarmInfo.put("alarmSummary", String.join("、", alarmDescriptions));
        
        // 按级别分类
        Map<String, List<String>> alarmsByLevel = new HashMap<>();
        alarmsByLevel.put("高", new ArrayList<>());
        alarmsByLevel.put("中", new ArrayList<>());
        alarmsByLevel.put("低", new ArrayList<>());
        
        for (AlarmType alarmType : AlarmType.values()) {
            if (Bit.isTrue(alarmBit, alarmType.getBit())) {
                alarmsByLevel.get(alarmType.getLevel()).add(alarmType.getDescription());
            }
        }
        alarmInfo.put("alarmsByLevel", alarmsByLevel);
        
        // 最高报警级别
        String highestLevel = "无";
        if (!alarmsByLevel.get("高").isEmpty()) {
            highestLevel = "高";
        } else if (!alarmsByLevel.get("中").isEmpty()) {
            highestLevel = "中";
        } else if (!alarmsByLevel.get("低").isEmpty()) {
            highestLevel = "低";
        }
        alarmInfo.put("highestAlarmLevel", highestLevel);
        
        // 原始报警位值
        alarmInfo.put("rawAlarmBit", alarmBit);
        alarmInfo.put("alarmBitHex", String.format("0x%08X", alarmBit));
        
        return alarmInfo;
    }
    
    /**
     * 获取报警状态简要描述
     * @param alarmBit 报警位
     * @return 报警描述
     */
    public static String getAlarmSummary(int alarmBit) {
        if (alarmBit == 0) {
            return "正常";
        }
        
        List<String> highPriorityAlarms = new ArrayList<>();
        List<String> mediumPriorityAlarms = new ArrayList<>();
        
        for (AlarmType alarmType : AlarmType.values()) {
            if (Bit.isTrue(alarmBit, alarmType.getBit())) {
                if ("高".equals(alarmType.getLevel())) {
                    highPriorityAlarms.add(alarmType.getDescription());
                } else if ("中".equals(alarmType.getLevel())) {
                    mediumPriorityAlarms.add(alarmType.getDescription());
                }
            }
        }
        
        if (!highPriorityAlarms.isEmpty()) {
            return "高危:" + String.join("、", highPriorityAlarms);
        } else if (!mediumPriorityAlarms.isEmpty()) {
            return "警告:" + String.join("、", mediumPriorityAlarms);
        } else {
            return "轻微报警";
        }
    }
    
    /**
     * 检查是否存在高危报警
     * @param alarmBit 报警位
     * @return 是否存在高危报警
     */
    public static boolean hasHighPriorityAlarm(int alarmBit) {
        for (AlarmType alarmType : AlarmType.values()) {
            if ("高".equals(alarmType.getLevel()) && Bit.isTrue(alarmBit, alarmType.getBit())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否存在紧急报警
     * @param alarmBit 报警位
     * @return 是否存在紧急报警
     */
    public static boolean hasEmergencyAlarm(int alarmBit) {
        return Bit.isTrue(alarmBit, AlarmType.EMERGENCY.getBit());
    }
    
    /**
     * 获取指定级别的报警列表
     * @param alarmBit 报警位
     * @param level 报警级别（高、中、低）
     * @return 指定级别的报警列表
     */
    public static List<String> getAlarmsByLevel(int alarmBit, String level) {
        List<String> alarms = new ArrayList<>();
        
        for (AlarmType alarmType : AlarmType.values()) {
            if (level.equals(alarmType.getLevel()) && Bit.isTrue(alarmBit, alarmType.getBit())) {
                alarms.add(alarmType.getDescription());
            }
        }
        
        return alarms;
    }
    
    /**
     * 检查特定报警是否激活
     * @param alarmBit 报警位
     * @param alarmType 报警类型
     * @return 是否激活
     */
    public static boolean isAlarmActive(int alarmBit, AlarmType alarmType) {
        return Bit.isTrue(alarmBit, alarmType.getBit());
    }
    
    /**
     * 获取所有激活的报警类型
     * @param alarmBit 报警位
     * @return 激活的报警类型列表
     */
    public static List<AlarmType> getActiveAlarmTypes(int alarmBit) {
        List<AlarmType> activeTypes = new ArrayList<>();
        
        for (AlarmType alarmType : AlarmType.values()) {
            if (Bit.isTrue(alarmBit, alarmType.getBit())) {
                activeTypes.add(alarmType);
            }
        }
        
        return activeTypes;
    }
}
